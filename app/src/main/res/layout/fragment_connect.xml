<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.connect.ConnectFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:paddingBottom="80dp">



        <!-- Device Selection -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="USB Serial Device"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/spinner_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp" />

                <Button
                    android:id="@+id/button_refresh_devices"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Refresh Devices"
                    style="@style/CustomButton.Small" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Connection Control with Baud Rate -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp"
            android:gravity="center_vertical">

            <Spinner
                android:id="@+id/spinner_baud_rate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="12dp"
                android:background="@drawable/spinner_background" />

            <Button
                android:id="@+id/button_connection_toggle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:text="Connect"
                style="@style/CustomButton.Primary.Filled" />

        </LinearLayout>

        <!-- Data Display -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Received Data"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <Button
                        android:id="@+id/button_clear_data"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Clear"
                        style="@style/CustomButton.Text" />

                </LinearLayout>

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:background="?attr/colorSecondaryVariant"
                    android:padding="8dp">

                    <TextView
                        android:id="@+id/text_received_data"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="?attr/colorOnSurface"
                        android:textSize="12sp"
                        android:fontFamily="monospace"
                        android:text="No data received..." />

                </ScrollView>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Send Data -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardElevation="4dp"
            app:cardCornerRadius="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Send Data"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_send_data"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Enter data to send..." />

                    </com.google.android.material.textfield.TextInputLayout>

                    <Button
                        android:id="@+id/button_send_data"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Send"
                        android:enabled="false"
                        style="@style/CustomButton.Primary" />

                </LinearLayout>

                <!-- Loopback Test Button -->
                <Button
                    android:id="@+id/button_test_loopback"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Test Loopback (RX-TX Connected)"
                    android:enabled="false"
                    style="@style/CustomButton.Text" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>
</ScrollView>
