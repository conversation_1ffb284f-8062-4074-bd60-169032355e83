package com.sartorua.thrust_tester.ui.connect

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.sartorua.thrust_tester.usb.ConnectionState
import com.sartorua.thrust_tester.usb.UsbSerialDevice
import com.sartorua.thrust_tester.usb.UsbSerialManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class ConnectViewModel(application: Application) : AndroidViewModel(application) {

    private val usbSerialManager = UsbSerialManager(application)

    // Expose USB manager state flows
    val connectionState: StateFlow<ConnectionState> = usbSerialManager.connectionState
    val availableDevices: StateFlow<List<UsbSerialDevice>> = usbSerialManager.availableDevices
    val receivedData: StateFlow<String> = usbSerialManager.receivedData
    val errorMessage: StateFlow<String?> = usbSerialManager.errorMessage

    // UI state
    private val _selectedBaudRate = MutableStateFlow(9600)
    val selectedBaudRate: StateFlow<Int> = _selectedBaudRate.asStateFlow()

    private val _selectedDevice = MutableStateFlow<UsbSerialDevice?>(null)
    val selectedDevice: StateFlow<UsbSerialDevice?> = _selectedDevice.asStateFlow()

    val baudRateOptions = listOf(9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600)

    fun refreshDevices() {
        usbSerialManager.refreshDeviceList()
    }

    fun selectDevice(device: UsbSerialDevice) {
        _selectedDevice.value = device
    }

    fun setBaudRate(baudRate: Int) {
        _selectedBaudRate.value = baudRate
    }

    fun connect() {
        val device = _selectedDevice.value
        if (device != null) {
            usbSerialManager.requestConnection(device, _selectedBaudRate.value)
        }
    }

    fun disconnect() {
        usbSerialManager.disconnect()
    }

    fun sendData(data: String) {
        usbSerialManager.sendData(data)
    }

    fun clearReceivedData() {
        usbSerialManager.clearReceivedData()
    }

    fun clearError() {
        usbSerialManager.clearError()
    }

    fun testLoopback() {
        usbSerialManager.testLoopback()
    }

    override fun onCleared() {
        super.onCleared()
        usbSerialManager.cleanup()
    }
}
